@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expand {
  from {
    width: 0;
  }
  to {
    width: 6rem;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-expand {
  animation: expand 1.5s ease-out;
}

/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  transition: all 0.2s ease;
  border: 2px solid #ffffff;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
  background: #2563eb;
}

.slider::-moz-range-thumb {
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
  background: #2563eb;
}

/* Line clamp utility */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Enhanced flowing liquid effects */
.flowing-liquid {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 300%;
  height: 300%;
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.3) 0%, rgba(147, 197, 253, 0.2) 30%, rgba(99, 102, 241, 0.1) 60%, transparent 80%);
  animation: flow 20s ease-in-out infinite;
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  filter: blur(1px);
}

.flowing-liquid-2 {
  position: absolute;
  top: -30%;
  left: -30%;
  width: 250%;
  height: 250%;
  background: radial-gradient(ellipse at center, rgba(168, 85, 247, 0.25) 0%, rgba(196, 181, 253, 0.15) 40%, rgba(139, 92, 246, 0.1) 70%, transparent 90%);
  animation: flow2 25s ease-in-out infinite reverse;
  border-radius: 40% 60% 70% 30% / 50% 70% 30% 60%;
  filter: blur(1px);
}

.flowing-liquid-3 {
  position: absolute;
  top: -20%;
  left: -20%;
  width: 200%;
  height: 200%;
  background: radial-gradient(ellipse at center, rgba(236, 72, 153, 0.2) 0%, rgba(251, 207, 232, 0.12) 50%, transparent 80%);
  animation: flow3 30s ease-in-out infinite;
  border-radius: 70% 30% 40% 60% / 40% 70% 60% 30%;
  filter: blur(0.5px);
}

@keyframes flow {
  0%, 100% {
    transform: translateX(-20%) translateY(-20%) rotate(0deg) scale(1);
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  25% {
    transform: translateX(-10%) translateY(-30%) rotate(90deg) scale(1.2);
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
  50% {
    transform: translateX(-30%) translateY(-10%) rotate(180deg) scale(0.8);
    border-radius: 70% 30% 40% 60% / 40% 70% 60% 30%;
  }
  75% {
    transform: translateX(-15%) translateY(-25%) rotate(270deg) scale(1.1);
    border-radius: 40% 70% 60% 30% / 70% 40% 50% 60%;
  }
}

@keyframes flow2 {
  0%, 100% {
    transform: translateX(-5%) translateY(-5%) rotate(45deg) scale(0.9);
    border-radius: 40% 60% 70% 30% / 50% 70% 30% 60%;
  }
  33% {
    transform: translateX(-15%) translateY(0%) rotate(135deg) scale(1.3);
    border-radius: 70% 30% 40% 60% / 40% 70% 60% 30%;
  }
  66% {
    transform: translateX(0%) translateY(-15%) rotate(225deg) scale(0.7);
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
}

@keyframes flow3 {
  0%, 100% {
    transform: translateX(-10%) translateY(-10%) rotate(30deg) scale(1.1);
    border-radius: 50% 50% 40% 60% / 60% 40% 60% 40%;
  }
  40% {
    transform: translateX(-5%) translateY(-20%) rotate(150deg) scale(0.9);
    border-radius: 60% 40% 50% 50% / 40% 60% 40% 60%;
  }
  80% {
    transform: translateX(-20%) translateY(-5%) rotate(270deg) scale(1.2);
    border-radius: 40% 60% 60% 40% / 50% 50% 60% 40%;
  }
}

/* Floating dots */
.floating-dots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.floating-dot {
  position: absolute;
  border-radius: 50%;
  animation: floatHorizontal 15s linear infinite;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Black dots - most visible */
.floating-dot.black {
  width: 8px;
  height: 8px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Blue dots */
.floating-dot.blue {
  width: 6px;
  height: 6px;
  background: rgba(59, 130, 246, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Purple dots */
.floating-dot.purple {
  width: 10px;
  height: 10px;
  background: rgba(168, 85, 247, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Horizontal floating animation */
@keyframes floatHorizontal {
  0% {
    transform: translateX(-50px) translateY(0px) rotate(0deg);
    opacity: 0;
  }
  5% {
    opacity: 1;
  }
  95% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 50px)) translateY(-20px) rotate(360deg);
    opacity: 0;
  }
}

/* Alternate animation for some dots */
.floating-dot:nth-child(even) {
  animation: floatHorizontalReverse 25s linear infinite;
}

@keyframes floatHorizontalReverse {
  0% {
    transform: translateX(calc(100vw + 50px)) translateY(0px) rotate(0deg);
    opacity: 0;
  }
  5% {
    opacity: 1;
  }
  95% {
    opacity: 1;
  }
  100% {
    transform: translateX(-50px) translateY(20px) rotate(-360deg);
    opacity: 0;
  }
}

/* Vertical floating for some variety */
.floating-dot:nth-child(3n) {
  animation: floatVertical 18s linear infinite;
}

@keyframes floatVertical {
  0% {
    transform: translateY(100vh) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-50px) translateX(30px) rotate(180deg);
    opacity: 0;
  }
}

/* Glass panel effect */
.glass-panel {
  position: relative;
  overflow: hidden;
}

.glass-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 4s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Enhanced slider styles for light theme */
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  cursor: pointer;
  border: 3px solid white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  cursor: pointer;
  border: 3px solid white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.slider {
  transition: all 0.2s ease;
}

.slider:hover {
  transform: translateY(-1px);
}

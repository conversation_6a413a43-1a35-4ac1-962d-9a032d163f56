@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Design System Variables */
:root {
  --primary-bg: #fafbfc;
  --secondary-bg: rgba(255, 255, 255, 0.85);
  --accent-bg: rgba(255, 255, 255, 0.95);
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --border-light: rgba(226, 232, 240, 0.8);
  --border-medium: rgba(203, 213, 224, 0.9);
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes expand {
  from {
    width: 0;
  }
  to {
    width: 6rem;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

.animate-expand {
  animation: expand 1.5s ease-out;
}

/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  transition: all 0.2s ease;
  border: 2px solid #ffffff;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
  background: #2563eb;
}

.slider::-moz-range-thumb {
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
  background: #2563eb;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Modern scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus styles */
.focus-ring:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Premium Interactive Elements */
.sample-button {
  background: linear-gradient(145deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.8) 100%);
  border: 1px solid rgba(203, 213, 224, 0.6);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.sample-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.sample-button:hover {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px) scale(1.02);
}

.sample-button:hover::before {
  left: 100%;
}

.sample-button:active {
  transform: translateY(0) scale(1.01);
  transition: all 0.1s ease;
}

/* Enhanced Audio Controls */
.audio-control-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.audio-control-button:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e3a8a 100%);
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-2px) scale(1.05);
}

/* Selective smooth transitions - exclude inputs and sliders */
*:not(input):not(audio):not(video) {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Ensure sliders and audio have no conflicting transitions */
input[type="range"], audio, video {
  transition: none !important;
}

input[type="range"]::-webkit-slider-thumb {
  transition: background 0.2s ease, box-shadow 0.2s ease !important;
}

/* Flowing Particle Animation Background - Matching Reference Image */
.particle-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

/* Base gradient background */
.particle-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 25%, #e2e8f0 50%, #f8fafc 75%, #ffffff 100%);
}

/* Organic Flowing Wave Layers - Enhanced Fluid Motion */
.wave-layer {
  position: absolute;
  width: 150%;
  height: 150%;
  top: -25%;
  left: -25%;
  will-change: transform;
}

.wave-layer-1 {
  background: radial-gradient(ellipse 1200px 600px at 40% 60%, rgba(59, 130, 246, 0.06) 0%, rgba(147, 197, 253, 0.03) 30%, transparent 60%);
  animation: organic-flow-1 40s ease-in-out infinite;
  transform-origin: center center;
}

.wave-layer-2 {
  background: radial-gradient(ellipse 900px 450px at 60% 40%, rgba(99, 102, 241, 0.04) 0%, rgba(196, 181, 253, 0.02) 40%, transparent 70%);
  animation: organic-flow-2 50s ease-in-out infinite reverse;
  transform-origin: center center;
}

.wave-layer-3 {
  background: radial-gradient(ellipse 600px 300px at 30% 70%, rgba(168, 85, 247, 0.03) 0%, rgba(221, 214, 254, 0.015) 50%, transparent 80%);
  animation: organic-flow-3 60s ease-in-out infinite;
  transform-origin: center center;
}

.wave-layer-4 {
  background: radial-gradient(ellipse 1500px 750px at 70% 30%, rgba(71, 85, 105, 0.02) 0%, rgba(148, 163, 184, 0.01) 35%, transparent 65%);
  animation: organic-flow-4 70s ease-in-out infinite reverse;
  transform-origin: center center;
}

/* Organic Wave Flow Animations - Smooth River-like Motion */
@keyframes organic-flow-1 {
  0%, 100% {
    transform: translateX(-3%) translateY(-2%) rotate(0deg) scale(1);
  }
  20% {
    transform: translateX(1%) translateY(-5%) rotate(0.5deg) scale(1.05);
  }
  40% {
    transform: translateX(-6%) translateY(-1%) rotate(-0.3deg) scale(0.98);
  }
  60% {
    transform: translateX(2%) translateY(-4%) rotate(0.2deg) scale(1.02);
  }
  80% {
    transform: translateX(-4%) translateY(-3%) rotate(-0.1deg) scale(0.99);
  }
}

@keyframes organic-flow-2 {
  0%, 100% {
    transform: translateX(2%) translateY(1%) rotate(0.3deg) scale(0.95);
  }
  25% {
    transform: translateX(-4%) translateY(-3%) rotate(-0.5deg) scale(1.08);
  }
  50% {
    transform: translateX(3%) translateY(2%) rotate(0.2deg) scale(0.92);
  }
  75% {
    transform: translateX(-2%) translateY(-1%) rotate(-0.2deg) scale(1.03);
  }
}

@keyframes organic-flow-3 {
  0%, 100% {
    transform: translateX(-1%) translateY(3%) rotate(-0.2deg) scale(1.02);
  }
  30% {
    transform: translateX(4%) translateY(-1%) rotate(0.4deg) scale(0.96);
  }
  60% {
    transform: translateX(-3%) translateY(2%) rotate(-0.3deg) scale(1.06);
  }
  90% {
    transform: translateX(1%) translateY(-2%) rotate(0.1deg) scale(0.98);
  }
}

@keyframes organic-flow-4 {
  0%, 100% {
    transform: translateX(1%) translateY(-1%) rotate(0.1deg) scale(1.01);
  }
  35% {
    transform: translateX(-5%) translateY(2%) rotate(-0.4deg) scale(0.94);
  }
  70% {
    transform: translateX(3%) translateY(-3%) rotate(0.3deg) scale(1.07);
  }
}

/* High-Density Particle System - Exact Reference Image Match */
.particle-field {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  will-change: transform;
}

/* Tiny particles - most numerous, like dust */
.particle-tiny {
  width: 1px;
  height: 1px;
  background: rgba(71, 85, 105, 0.4);
  animation: river-flow-tiny 25s linear infinite;
}

/* Small particles - secondary layer */
.particle-small {
  width: 2px;
  height: 2px;
  background: rgba(51, 65, 85, 0.6);
  animation: river-flow-small 30s linear infinite;
}

/* Medium particles - main visible layer */
.particle-medium {
  width: 3px;
  height: 3px;
  background: rgba(30, 41, 59, 0.7);
  animation: river-flow-medium 35s linear infinite;
}

/* Large particles - prominent features */
.particle-large {
  width: 4px;
  height: 4px;
  background: rgba(15, 23, 42, 0.8);
  animation: river-flow-large 40s linear infinite;
}

/* Extra large particles - rare focal points */
.particle-xlarge {
  width: 5px;
  height: 5px;
  background: rgba(15, 23, 42, 0.9);
  animation: river-flow-xlarge 45s linear infinite;
}

/* River-like Flow Animations - Matching Reference Image */

/* Tiny particles - fastest, most chaotic flow */
@keyframes river-flow-tiny {
  0% {
    transform: translateX(-50px) translateY(0px);
    opacity: 0;
  }
  5% {
    opacity: 0.8;
  }
  95% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(calc(100vw + 50px)) translateY(-10px);
    opacity: 0;
  }
}

/* Small particles - gentle wave motion */
@keyframes river-flow-small {
  0% {
    transform: translateX(-50px) translateY(0px);
    opacity: 0;
  }
  5% {
    opacity: 0.9;
  }
  20% {
    transform: translateX(20vw) translateY(-5px);
  }
  40% {
    transform: translateX(40vw) translateY(8px);
  }
  60% {
    transform: translateX(60vw) translateY(-3px);
  }
  80% {
    transform: translateX(80vw) translateY(6px);
  }
  95% {
    opacity: 0.9;
  }
  100% {
    transform: translateX(calc(100vw + 50px)) translateY(-8px);
    opacity: 0;
  }
}

/* Medium particles - smooth flowing motion */
@keyframes river-flow-medium {
  0% {
    transform: translateX(-60px) translateY(0px);
    opacity: 0;
  }
  8% {
    opacity: 1;
  }
  25% {
    transform: translateX(25vw) translateY(-8px);
  }
  45% {
    transform: translateX(45vw) translateY(12px);
  }
  65% {
    transform: translateX(65vw) translateY(-6px);
  }
  85% {
    transform: translateX(85vw) translateY(10px);
  }
  92% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 60px)) translateY(-12px);
    opacity: 0;
  }
}

/* Large particles - steady, prominent flow */
@keyframes river-flow-large {
  0% {
    transform: translateX(-80px) translateY(0px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  30% {
    transform: translateX(30vw) translateY(-12px);
  }
  50% {
    transform: translateX(50vw) translateY(15px);
  }
  70% {
    transform: translateX(70vw) translateY(-8px);
  }
  90% {
    opacity: 1;
    transform: translateX(90vw) translateY(12px);
  }
  100% {
    transform: translateX(calc(100vw + 80px)) translateY(-15px);
    opacity: 0;
  }
}

/* Extra large particles - slow, majestic flow */
@keyframes river-flow-xlarge {
  0% {
    transform: translateX(-100px) translateY(0px);
    opacity: 0;
  }
  12% {
    opacity: 1;
  }
  35% {
    transform: translateX(35vw) translateY(-18px);
  }
  55% {
    transform: translateX(55vw) translateY(20px);
  }
  75% {
    transform: translateX(75vw) translateY(-12px);
  }
  88% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 100px)) translateY(-20px);
    opacity: 0;
  }
}

/* Modern Glass Panel Effect */
.glass-panel {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-medium);
}

.glass-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  animation: shimmer 6s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Fixed Slider Styles - No Interference */
.modern-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  border-radius: 3px;
  background: linear-gradient(to right, #e2e8f0, #cbd5e1);
  outline: none;
  cursor: pointer;
}

.modern-slider:hover {
  background: linear-gradient(to right, #cbd5e1, #94a3b8);
}

.modern-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-slider::-webkit-slider-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.modern-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Premium Button Styles */
.modern-button {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: translateY(0);
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.modern-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4), 0 4px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e3a8a 100%);
}

.modern-button:hover::before {
  left: 100%;
}

.modern-button:active {
  transform: translateY(-1px) scale(1.01);
  transition: all 0.1s ease;
}

.modern-button:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 50%, #475569 100%);
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.2);
  transform: translateY(0);
}

.modern-button:disabled:hover {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.2);
}

/* Modern Input Styles */
.modern-input {
  transition: all 0.3s ease;
  border: 2px solid var(--border-light);
  background: var(--secondary-bg);
}

.modern-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--accent-bg);
}

/* Premium Card Styles */
.modern-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-card:hover {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px) scale(1.01);
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
}

.modern-card:hover::before {
  opacity: 1;
}

/* Enhanced Glass Panel */
.glass-panel {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
}

.glass-panel::before {
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent);
  animation: shimmer 8s infinite;
}

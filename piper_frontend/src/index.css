@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Design System Variables */
:root {
  --primary-bg: #fafbfc;
  --secondary-bg: rgba(255, 255, 255, 0.85);
  --accent-bg: rgba(255, 255, 255, 0.95);
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --border-light: rgba(226, 232, 240, 0.8);
  --border-medium: rgba(203, 213, 224, 0.9);
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes expand {
  from {
    width: 0;
  }
  to {
    width: 6rem;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

.animate-expand {
  animation: expand 1.5s ease-out;
}

/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  transition: all 0.2s ease;
  border: 2px solid #ffffff;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
  background: #2563eb;
}

.slider::-moz-range-thumb {
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
  background: #2563eb;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Modern scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus styles */
.focus-ring:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Flowing Particle Animation Background - Matching Reference Image */
.particle-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

/* Base gradient background */
.particle-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 25%, #e2e8f0 50%, #f8fafc 75%, #ffffff 100%);
}

/* Flowing wave layers */
.wave-layer {
  position: absolute;
  width: 120%;
  height: 120%;
  top: -10%;
  left: -10%;
}

.wave-layer-1 {
  background: radial-gradient(ellipse 800px 400px at 50% 50%, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.04) 40%, transparent 70%);
  animation: wave-flow-1 25s ease-in-out infinite;
  transform-origin: center center;
}

.wave-layer-2 {
  background: radial-gradient(ellipse 600px 300px at 30% 70%, rgba(99, 102, 241, 0.06) 0%, rgba(196, 181, 253, 0.03) 50%, transparent 80%);
  animation: wave-flow-2 30s ease-in-out infinite reverse;
  transform-origin: center center;
}

.wave-layer-3 {
  background: radial-gradient(ellipse 400px 200px at 70% 30%, rgba(168, 85, 247, 0.05) 0%, rgba(221, 214, 254, 0.025) 60%, transparent 90%);
  animation: wave-flow-3 35s ease-in-out infinite;
  transform-origin: center center;
}

@keyframes wave-flow-1 {
  0%, 100% {
    transform: translateX(-5%) translateY(-3%) rotate(0deg) scale(1);
  }
  25% {
    transform: translateX(2%) translateY(-8%) rotate(1deg) scale(1.1);
  }
  50% {
    transform: translateX(-8%) translateY(-2%) rotate(-1deg) scale(0.95);
  }
  75% {
    transform: translateX(3%) translateY(-6%) rotate(0.5deg) scale(1.05);
  }
}

@keyframes wave-flow-2 {
  0%, 100% {
    transform: translateX(3%) translateY(2%) rotate(0.5deg) scale(0.9);
  }
  33% {
    transform: translateX(-6%) translateY(-4%) rotate(-0.8deg) scale(1.2);
  }
  66% {
    transform: translateX(4%) translateY(3%) rotate(0.3deg) scale(0.85);
  }
}

@keyframes wave-flow-3 {
  0%, 100% {
    transform: translateX(-2%) translateY(4%) rotate(-0.3deg) scale(1.1);
  }
  40% {
    transform: translateX(5%) translateY(-2%) rotate(0.7deg) scale(0.9);
  }
  80% {
    transform: translateX(-4%) translateY(1%) rotate(-0.5deg) scale(1.15);
  }
}

/* Sophisticated Particle System - Matching Reference Image */
.particle-field {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}

/* Primary particles - dark and prominent like in reference */
.particle-primary {
  width: 4px;
  height: 4px;
  background: rgba(30, 41, 59, 0.8);
  box-shadow: 0 0 2px rgba(30, 41, 59, 0.4);
  animation: flow-horizontal 20s linear infinite;
}

/* Secondary particles - lighter */
.particle-secondary {
  width: 3px;
  height: 3px;
  background: rgba(71, 85, 105, 0.6);
  box-shadow: 0 0 1px rgba(71, 85, 105, 0.3);
  animation: flow-diagonal 25s linear infinite;
}

/* Accent particles - subtle blue tint */
.particle-accent {
  width: 2px;
  height: 2px;
  background: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 1px rgba(59, 130, 246, 0.3);
  animation: flow-wave 30s linear infinite;
}

/* Large feature particles */
.particle-large {
  width: 6px;
  height: 6px;
  background: rgba(15, 23, 42, 0.7);
  box-shadow: 0 0 3px rgba(15, 23, 42, 0.4);
  animation: flow-slow 35s linear infinite;
}

/* Horizontal flow animation - main movement pattern */
@keyframes flow-horizontal {
  0% {
    transform: translateX(-20px) translateY(0px);
    opacity: 0;
  }
  5% {
    opacity: 1;
  }
  95% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 20px)) translateY(-10px);
    opacity: 0;
  }
}

/* Diagonal flow with wave motion */
@keyframes flow-diagonal {
  0% {
    transform: translateX(-15px) translateY(10px);
    opacity: 0;
  }
  8% {
    opacity: 1;
  }
  92% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 15px)) translateY(-15px);
    opacity: 0;
  }
}

/* Wave-like motion */
@keyframes flow-wave {
  0% {
    transform: translateX(-10px) translateY(0px);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translateX(10vw) translateY(-5px);
  }
  30% {
    transform: translateX(30vw) translateY(5px);
  }
  50% {
    transform: translateX(50vw) translateY(-3px);
  }
  70% {
    transform: translateX(70vw) translateY(4px);
  }
  90% {
    opacity: 1;
    transform: translateX(90vw) translateY(-2px);
  }
  100% {
    transform: translateX(calc(100vw + 10px)) translateY(0px);
    opacity: 0;
  }
}

/* Slow, steady flow for large particles */
@keyframes flow-slow {
  0% {
    transform: translateX(-25px) translateY(5px);
    opacity: 0;
  }
  3% {
    opacity: 1;
  }
  97% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 25px)) translateY(-8px);
    opacity: 0;
  }
}

/* Modern Glass Panel Effect */
.glass-panel {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-medium);
}

.glass-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  animation: shimmer 6s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Modern Slider Styles */
.modern-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  border-radius: 3px;
  background: linear-gradient(to right, #e2e8f0, #cbd5e1);
  outline: none;
  transition: all 0.3s ease;
}

.modern-slider:hover {
  background: linear-gradient(to right, #cbd5e1, #94a3b8);
}

.modern-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: var(--shadow-soft);
  transition: all 0.2s ease;
}

.modern-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-medium);
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

.modern-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: var(--shadow-soft);
  transition: all 0.2s ease;
}

/* Modern Button Styles */
.modern-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  transform: translateY(0);
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-large);
}

.modern-button:active {
  transform: translateY(0);
}

/* Modern Input Styles */
.modern-input {
  transition: all 0.3s ease;
  border: 2px solid var(--border-light);
  background: var(--secondary-bg);
}

.modern-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--accent-bg);
}

/* Modern Card Styles */
.modern-card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-soft);
  transition: all 0.3s ease;
}

.modern-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-1px);
}

import { useState, useEffect, useRef } from 'react';
import WaveSurfer from 'wavesurfer.js';

function App() {
  const [text, setText] = useState('नेपाल एक सुन्दर हिमालयी देश हो जहाँ विविधताले भरिएको संस्कृति र परम्परा छ। यहाँका मानिसहरू धेरै मिलनसार र दयालु छन्। नेपालमा अनेकौं भाषा र जातिका मानिसहरू एकसाथ बस्छन्।');
  const [speakerId, setSpeakerId] = useState(0);
  const [speechRate, setSpeechRate] = useState(1.0);
  const [noiseScale, setNoiseScale] = useState(0.667);
  const [noiseW, setNoiseW] = useState(0.8);
  const [sentenceSilence, setSentenceSilence] = useState(0.0);
  const [isLoading, setIsLoading] = useState(false);
  const [voiceInfo, setVoiceInfo] = useState(null);
  const [speakers, setSpeakers] = useState([]);
  const [apiStatus, setApiStatus] = useState('checking');
  const [audioUrl, setAudioUrl] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [fontSize, setFontSize] = useState(16);

  const waveformRef = useRef(null);
  const wavesurfer = useRef(null);

  const API_BASE = 'http://localhost:8000';

  // Check API status and load voice info
  useEffect(() => {
    checkApiStatus();
    loadVoiceInfo();
    loadSpeakers();
  }, []);

  const checkApiStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/`);
      const data = await response.json();
      setApiStatus(data.voice_loaded ? 'ready' : 'no-voice');
    } catch (error) {
      setApiStatus('offline');
    }
  };

  const loadVoiceInfo = async () => {
    try {
      const response = await fetch(`${API_BASE}/voice/info`);
      if (response.ok) {
        const data = await response.json();
        setVoiceInfo(data);
      }
    } catch (error) {
      console.error('Failed to load voice info:', error);
    }
  };

  const loadSpeakers = async () => {
    try {
      const response = await fetch(`${API_BASE}/speakers`);
      if (response.ok) {
        const data = await response.json();
        setSpeakers(data.speakers || []);
      }
    } catch (error) {
      console.error('Failed to load speakers:', error);
    }
  };

  const synthesizeSpeech = async () => {
    if (!text.trim()) return;

    setIsLoading(true);
    setAudioUrl(null);

    // Destroy existing wavesurfer instance
    if (wavesurfer.current) {
      wavesurfer.current.destroy();
      wavesurfer.current = null;
    }

    try {
      const params = new URLSearchParams({
        text: text,
        speaker_id: speakerId,
        speech_rate: speechRate,
        noise_scale: noiseScale,
        noise_w: noiseW,
        sentence_silence: sentenceSilence
      });

      const response = await fetch(`${API_BASE}/synthesize?${params}`);

      if (response.ok) {
        const audioBlob = await response.blob();
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);

        // Initialize WaveSurfer after audio is ready
        setTimeout(() => initializeWaveSurfer(url), 100);
      } else {
        alert('Failed to synthesize speech');
      }
    } catch (error) {
      console.error('Synthesis error:', error);
      alert('Error connecting to API');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeWaveSurfer = (audioUrl) => {
    if (waveformRef.current && audioUrl) {
      wavesurfer.current = WaveSurfer.create({
        container: waveformRef.current,
        waveColor: '#cbd5e1',
        progressColor: '#3b82f6',
        cursorColor: '#1d4ed8',
        barWidth: 3,
        barRadius: 2,
        responsive: true,
        height: 60,
        normalize: true,
        backend: 'WebAudio',
        mediaControls: false,
      });

      wavesurfer.current.load(audioUrl);

      wavesurfer.current.on('ready', () => {
        setDuration(wavesurfer.current.getDuration());
      });

      wavesurfer.current.on('audioprocess', () => {
        setCurrentTime(wavesurfer.current.getCurrentTime());
      });

      wavesurfer.current.on('play', () => {
        setIsPlaying(true);
      });

      wavesurfer.current.on('pause', () => {
        setIsPlaying(false);
      });

      wavesurfer.current.on('finish', () => {
        setIsPlaying(false);
        setCurrentTime(0);
      });
    }
  };

  const togglePlayPause = () => {
    if (wavesurfer.current) {
      wavesurfer.current.playPause();
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusText = () => {
    switch (apiStatus) {
      case 'ready': return 'API Ready';
      case 'no-voice': return 'No Voice Loaded';
      case 'offline': return 'API Offline';
      default: return 'Checking...';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 text-slate-800 overflow-hidden relative">
      {/* Sophisticated Animated Background - Matching Reference Image */}
      <div className="particle-container">
        {/* Base gradient background */}
        <div className="particle-background"></div>

        {/* Organic flowing wave layers */}
        <div className="wave-layer wave-layer-1"></div>
        <div className="wave-layer wave-layer-2"></div>
        <div className="wave-layer wave-layer-3"></div>
        <div className="wave-layer wave-layer-4"></div>

        {/* Ultra High-Density Particle Field - Clustered in Middle/Bottom */}
        <div className="particle-field">
          {/* Tiny particles - massive density, clustered in middle/bottom */}
          {[...Array(300)].map((_, i) => {
            // Cluster particles more in middle and bottom areas
            const yPosition = 30 + Math.random() * 70; // 30% to 100% from top
            const xStart = -10 + Math.random() * 120; // Wider spawn area
            return (
              <div
                key={`tiny-${i}`}
                className="particle particle-tiny"
                style={{
                  left: `${xStart}%`,
                  top: `${yPosition}%`,
                  animationDelay: `${Math.random() * 25}s`,
                  animationDuration: `${20 + Math.random() * 10}s`
                }}
              ></div>
            );
          })}

          {/* Small particles - high density, middle/bottom focus */}
          {[...Array(200)].map((_, i) => {
            const yPosition = 40 + Math.random() * 60; // 40% to 100% from top
            const xStart = -10 + Math.random() * 120;
            return (
              <div
                key={`small-${i}`}
                className="particle particle-small"
                style={{
                  left: `${xStart}%`,
                  top: `${yPosition}%`,
                  animationDelay: `${Math.random() * 30}s`,
                  animationDuration: `${25 + Math.random() * 10}s`
                }}
              ></div>
            );
          })}

          {/* Medium particles - concentrated in center/bottom */}
          {[...Array(150)].map((_, i) => {
            const yPosition = 50 + Math.random() * 50; // 50% to 100% from top
            const xStart = -10 + Math.random() * 120;
            return (
              <div
                key={`medium-${i}`}
                className="particle particle-medium"
                style={{
                  left: `${xStart}%`,
                  top: `${yPosition}%`,
                  animationDelay: `${Math.random() * 35}s`,
                  animationDuration: `${30 + Math.random() * 10}s`
                }}
              ></div>
            );
          })}

          {/* Large particles - bottom heavy distribution */}
          {[...Array(80)].map((_, i) => {
            const yPosition = 60 + Math.random() * 40; // 60% to 100% from top
            const xStart = -10 + Math.random() * 120;
            return (
              <div
                key={`large-${i}`}
                className="particle particle-large"
                style={{
                  left: `${xStart}%`,
                  top: `${yPosition}%`,
                  animationDelay: `${Math.random() * 40}s`,
                  animationDuration: `${35 + Math.random() * 10}s`
                }}
              ></div>
            );
          })}

          {/* Extra large particles - mostly bottom area */}
          {[...Array(40)].map((_, i) => {
            const yPosition = 70 + Math.random() * 30; // 70% to 100% from top
            const xStart = -10 + Math.random() * 120;
            return (
              <div
                key={`xlarge-${i}`}
                className="particle particle-xlarge"
                style={{
                  left: `${xStart}%`,
                  top: `${yPosition}%`,
                  animationDelay: `${Math.random() * 45}s`,
                  animationDuration: `${40 + Math.random() * 10}s`
                }}
              ></div>
            );
          })}
        </div>
      </div>

      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Modern Header */}
        <header className="bg-white/80 backdrop-blur-md border-b border-slate-200/60 shadow-sm">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 tracking-tight">
                    Piper TTS
                  </h1>
                  <p className="text-sm text-slate-600">Neural Text-to-Speech for Nepali</p>
                </div>
              </div>

              <div className="flex items-center space-x-6">
                <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
                  apiStatus === 'ready' ? 'bg-green-50 text-green-700' :
                  apiStatus === 'offline' ? 'bg-red-50 text-red-700' :
                  'bg-yellow-50 text-yellow-700'
                }`}>
                  <div className={`w-2 h-2 rounded-full ${
                    apiStatus === 'ready' ? 'bg-green-500 animate-pulse' :
                    apiStatus === 'offline' ? 'bg-red-500' :
                    'bg-yellow-500'
                  }`}></div>
                  <span className="text-sm font-medium">{getStatusText()}</span>
                </div>

                {voiceInfo && (
                  <div className="hidden md:flex items-center space-x-4 text-sm text-slate-600">
                    <div className="flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      <span>{voiceInfo.num_speakers} speakers</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                      </svg>
                      <span>{voiceInfo.sample_rate}Hz</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 max-w-7xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Text Input Section */}
            <div className="xl:col-span-2 space-y-6">
              <div className="modern-card glass-panel rounded-2xl p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-semibold text-slate-900">Text Input</h2>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-3">
                      <label className="block text-sm font-medium text-slate-700">
                        Text to Synthesize
                      </label>
                      <div className="flex items-center space-x-2">
                        <label className="text-xs text-slate-500">Font Size:</label>
                        <input
                          type="range"
                          min="12"
                          max="20"
                          value={fontSize}
                          onChange={(e) => setFontSize(parseInt(e.target.value))}
                          className="modern-slider w-16"
                        />
                        <span className="text-xs text-slate-500 w-8">{fontSize}px</span>
                      </div>
                    </div>
                    <textarea
                      value={text}
                      onChange={(e) => setText(e.target.value)}
                      style={{ fontSize: `${fontSize}px` }}
                      className="modern-input w-full h-32 p-4 rounded-xl resize-none text-slate-900 placeholder-slate-400 focus:outline-none"
                      placeholder="नेपाली पाठ यहाँ लेख्नुहोस्..."
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Speaker ({speakers.length} available)
                      </label>
                      <select
                        value={speakerId}
                        onChange={(e) => setSpeakerId(parseInt(e.target.value))}
                        className="modern-input w-full p-3 rounded-xl focus:outline-none"
                      >
                        {speakers.map((speaker) => (
                          <option key={speaker.speaker_id} value={speaker.speaker_id}>
                            Speaker {speaker.speaker_id}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Speech Rate: {speechRate}x
                      </label>
                      <input
                        type="range"
                        min="0.5"
                        max="2.0"
                        step="0.1"
                        value={speechRate}
                        onChange={(e) => setSpeechRate(parseFloat(e.target.value))}
                        className="modern-slider w-full"
                      />
                    </div>
                  </div>

                  {/* Generate Button */}
                  <button
                    onClick={synthesizeSpeech}
                    disabled={isLoading || apiStatus !== 'ready' || !text.trim()}
                    className="modern-button w-full text-white font-semibold py-4 px-6 rounded-xl disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center space-x-3">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Synthesizing...</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center space-x-2">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M8.586 17.414L12 14H8a2 2 0 01-2-2V8a2 2 0 012-2h4l3.414 3.414" />
                        </svg>
                        <span>Generate Speech</span>
                      </div>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Right Panel - Controls and Audio */}
            <div className="space-y-6">
              {/* Advanced Controls */}
              <div className="modern-card glass-panel rounded-2xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-slate-900">Advanced Settings</h3>
                  </div>
                  <button
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    className="p-2 text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded-lg transition-colors duration-200"
                  >
                    <svg className={`w-5 h-5 transform transition-transform duration-200 ${showAdvanced ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>

                {showAdvanced && (
                  <div className="space-y-4 animate-fade-in">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Noise Scale: {noiseScale.toFixed(2)}
                      </label>
                      <input
                        type="range"
                        min="0.0"
                        max="1.0"
                        step="0.01"
                        value={noiseScale}
                        onChange={(e) => setNoiseScale(parseFloat(e.target.value))}
                        className="modern-slider w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Phoneme Width: {noiseW.toFixed(2)}
                      </label>
                      <input
                        type="range"
                        min="0.0"
                        max="1.0"
                        step="0.01"
                        value={noiseW}
                        onChange={(e) => setNoiseW(parseFloat(e.target.value))}
                        className="modern-slider w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Sentence Silence: {sentenceSilence.toFixed(1)}s
                      </label>
                      <input
                        type="range"
                        min="0.0"
                        max="2.0"
                        step="0.1"
                        value={sentenceSilence}
                        onChange={(e) => setSentenceSilence(parseFloat(e.target.value))}
                        className="modern-slider w-full"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Audio Visualization */}
              {audioUrl && (
                <div className="modern-card glass-panel rounded-2xl p-6 animate-fade-in">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-slate-900">Audio Player</h3>
                  </div>

                  {/* Waveform */}
                  <div className="bg-slate-50 rounded-xl p-4 mb-4 border border-slate-200">
                    <div ref={waveformRef} className="w-full"></div>
                  </div>

                  {/* Audio Controls */}
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={togglePlayPause}
                      className="audio-control-button flex items-center justify-center w-12 h-12 text-white rounded-xl"
                    >
                      {isPlaying ? (
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                        </svg>
                      ) : (
                        <svg className="w-5 h-5 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      )}
                    </button>

                    <div className="flex-1">
                      <div className="flex justify-between text-xs text-slate-500 mb-2">
                        <span>{formatTime(currentTime)}</span>
                        <span>{formatTime(duration)}</span>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-100"
                          style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                        ></div>
                      </div>
                    </div>

                    <a
                      href={audioUrl}
                      download="piper_tts_output.wav"
                      className="flex items-center space-x-2 bg-slate-600 hover:bg-slate-700 text-white py-2 px-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <span className="text-sm">Download</span>
                    </a>
                  </div>
                </div>
              )}

              {/* Sample Texts */}
              <div className="modern-card glass-panel rounded-2xl p-6 flex-1">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900">Sample Texts</h3>
                </div>

                <div className="space-y-3 max-h-96 overflow-y-auto">
                  <button
                    onClick={() => setText('नमस्ते, म एक कृत्रिम बुद्धिमत्ता आधारित आवाज संश्लेषण प्रणाली हुँ। म नेपाली भाषामा बोल्न सक्छु र विभिन्न प्रकारका पाठहरूलाई प्राकृतिक आवाजमा रूपान्तरण गर्न सक्छु।')}
                    className="sample-button w-full p-4 rounded-xl text-left"
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">🤖</span>
                      <div className="font-semibold text-slate-900 text-sm">AI Introduction</div>
                    </div>
                    <div className="text-xs text-slate-600 line-clamp-2">नमस्ते, म एक कृत्रिम बुद्धिमत्ता आधारित आवाज संश्लेषण प्रणाली हुँ...</div>
                  </button>

                  <button
                    onClick={() => setText('हिमालयको काखमा बसेको नेपाल एक सुन्दर देश हो। यहाँका पहाडहरू, नदीहरू र जंगलहरूले प्राकृतिक सुन्दरताको अनुपम दृश्य प्रस्तुत गर्छन्।')}
                    className="sample-button w-full p-4 rounded-xl text-left"
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">🏔️</span>
                      <div className="font-semibold text-slate-900 text-sm">Nepal</div>
                    </div>
                    <div className="text-xs text-slate-600 line-clamp-2">हिमालयको काखमा बसेको नेपाल एक सुन्दर देश हो...</div>
                  </button>

                  <button
                    onClick={() => setText('प्रविधिको क्षेत्रमा नेपालले उल्लेखनीय प्रगति गरेको छ। आजकल धेरै युवाहरू सफ्टवेयर विकास र डिजिटल मार्केटिङमा काम गरिरहेका छन्।')}
                    className="sample-button w-full p-4 rounded-xl text-left"
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">💻</span>
                      <div className="font-semibold text-slate-900 text-sm">Technology</div>
                    </div>
                    <div className="text-xs text-slate-600 line-clamp-2">प्रविधिको क्षेत्रमा नेपालले उल्लेखनीय प्रगति गरेको छ...</div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;
